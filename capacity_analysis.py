#!/usr/bin/env python3
"""
水电站装机容量和时序容量因子深度分析
基于Nature Water论文数据进行MILP模型数据准备
"""

import pandas as pd
import geopandas as gpd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class HydropowerCapacityAnalyzer:
    def __init__(self, shapefile_path):
        """初始化分析器"""
        self.shapefile_path = shapefile_path
        self.gdf = None
        self.analysis_results = {}
        
    def load_and_prepare_data(self):
        """加载并预处理数据"""
        print("="*80)
        print("水电站装机容量和时序容量因子分析")
        print("基于Nature Water论文 - Xu et al. 2023")
        print("="*80)
        
        # 加载数据
        self.gdf = gpd.read_file(self.shapefile_path)
        self.gdf['longitude'] = self.gdf.geometry.x
        self.gdf['latitude'] = self.gdf.geometry.y
        
        print(f"✓ 数据加载完成: {len(self.gdf):,} 个水电站")
        
        # 添加站点类型标签
        self.gdf['station_type'] = self.gdf['Class'].map({
            1: 'reservoir',  # 大坝式
            2: 'run_of_river'  # 引水式
        })
        
        return True
    
    def analyze_capacity_calculation(self):
        """分析装机容量计算方法"""
        print("\n1. 装机容量计算可行性分析")
        print("-" * 60)
        
        # 检查Power字段特征
        power_stats = self.gdf['Power'].describe()
        print("Power字段统计信息 (年发电量 kWh):")
        print(f"  数据点数: {len(self.gdf):,}")
        print(f"  平均值: {power_stats['mean']/1e6:.2f} GWh")
        print(f"  中位数: {power_stats['50%']/1e6:.2f} GWh")
        print(f"  标准差: {power_stats['std']/1e6:.2f} GWh")
        print(f"  范围: {power_stats['min']/1e6:.2f} - {power_stats['max']/1e6:.2f} GWh")
        
        # 装机容量计算方法
        print("\n装机容量计算方法:")
        print("公式: 装机容量(MW) = 年发电量(kWh) / (8760小时 × 容量因子)")
        
        # 不同容量因子假设下的装机容量计算
        capacity_factors = {
            'conservative': 0.35,  # 保守估计
            'typical': 0.45,       # 典型值
            'optimistic': 0.55     # 乐观估计
        }
        
        print("\n不同容量因子假设下的装机容量计算:")
        for scenario, cf in capacity_factors.items():
            capacity_mw = self.gdf['Power'] / (8760 * cf * 1000)  # 转换为MW
            total_capacity = capacity_mw.sum()
            avg_capacity = capacity_mw.mean()
            
            print(f"  {scenario.capitalize()} (CF={cf}):")
            print(f"    总装机容量: {total_capacity:,.0f} MW")
            print(f"    平均单站容量: {avg_capacity:.2f} MW")
            print(f"    容量范围: {capacity_mw.min():.2f} - {capacity_mw.max():.2f} MW")
        
        # 保存典型值计算结果
        typical_cf = capacity_factors['typical']
        self.gdf['capacity_mw'] = self.gdf['Power'] / (8760 * typical_cf * 1000)
        
        self.analysis_results['capacity_calculation'] = {
            'method': '年发电量除以年运行小时数和容量因子',
            'formula': 'Capacity(MW) = Power(kWh) / (8760 × CF)',
            'capacity_factor_scenarios': capacity_factors,
            'typical_results': {
                'total_capacity_mw': float(self.gdf['capacity_mw'].sum()),
                'average_capacity_mw': float(self.gdf['capacity_mw'].mean()),
                'capacity_range': [float(self.gdf['capacity_mw'].min()), 
                                 float(self.gdf['capacity_mw'].max())]
            }
        }
        
        return True
    
    def analyze_station_type_differences(self):
        """分析不同站点类型的容量特征差异"""
        print("\n2. 站点类型容量特征差异分析")
        print("-" * 60)
        
        # 按站点类型分组分析
        type_analysis = self.gdf.groupby('station_type').agg({
            'Power': ['count', 'sum', 'mean', 'std', 'min', 'max'],
            'capacity_mw': ['sum', 'mean', 'std', 'min', 'max'],
            'LCOE': ['mean', 'std']
        }).round(3)
        
        print("按站点类型统计:")
        print("大坝式水电站 (Reservoir-type):")
        reservoir_data = self.gdf[self.gdf['station_type'] == 'reservoir']
        print(f"  站点数量: {len(reservoir_data):,}")
        print(f"  总发电量: {reservoir_data['Power'].sum()/1e12:.2f} TWh")
        print(f"  总装机容量: {reservoir_data['capacity_mw'].sum():,.0f} MW")
        print(f"  平均单站容量: {reservoir_data['capacity_mw'].mean():.2f} MW")
        print(f"  平均LCOE: {reservoir_data['LCOE'].mean():.4f} USD/kWh")
        
        print("\n引水式水电站 (Run-of-river):")
        ror_data = self.gdf[self.gdf['station_type'] == 'run_of_river']
        print(f"  站点数量: {len(ror_data):,}")
        print(f"  总发电量: {ror_data['Power'].sum()/1e12:.2f} TWh")
        print(f"  总装机容量: {ror_data['capacity_mw'].sum():,.0f} MW")
        print(f"  平均单站容量: {ror_data['capacity_mw'].mean():.2f} MW")
        print(f"  平均LCOE: {ror_data['LCOE'].mean():.4f} USD/kWh")
        
        # 容量分布分析
        print("\n容量规模分布:")
        capacity_ranges = [
            (0, 1, "微型 (<1 MW)"),
            (1, 10, "小型 (1-10 MW)"),
            (10, 50, "中型 (10-50 MW)"),
            (50, 100, "大型 (50-100 MW)"),
            (100, float('inf'), "超大型 (>100 MW)")
        ]
        
        for station_type in ['reservoir', 'run_of_river']:
            type_data = self.gdf[self.gdf['station_type'] == station_type]
            type_name = "大坝式" if station_type == 'reservoir' else "引水式"
            print(f"\n{type_name}水电站容量分布:")
            
            for min_cap, max_cap, label in capacity_ranges:
                if max_cap == float('inf'):
                    count = len(type_data[type_data['capacity_mw'] >= min_cap])
                    capacity_sum = type_data[type_data['capacity_mw'] >= min_cap]['capacity_mw'].sum()
                else:
                    count = len(type_data[(type_data['capacity_mw'] >= min_cap) & 
                                        (type_data['capacity_mw'] < max_cap)])
                    capacity_sum = type_data[(type_data['capacity_mw'] >= min_cap) & 
                                           (type_data['capacity_mw'] < max_cap)]['capacity_mw'].sum()
                
                percentage = (count / len(type_data)) * 100
                print(f"  {label}: {count:,} 个 ({percentage:.1f}%) | 总容量: {capacity_sum:,.0f} MW")
        
        self.analysis_results['station_type_analysis'] = {
            'reservoir': {
                'count': len(reservoir_data),
                'total_capacity_mw': float(reservoir_data['capacity_mw'].sum()),
                'average_capacity_mw': float(reservoir_data['capacity_mw'].mean()),
                'average_lcoe': float(reservoir_data['LCOE'].mean())
            },
            'run_of_river': {
                'count': len(ror_data),
                'total_capacity_mw': float(ror_data['capacity_mw'].sum()),
                'average_capacity_mw': float(ror_data['capacity_mw'].mean()),
                'average_lcoe': float(ror_data['LCOE'].mean())
            }
        }
        
        return True
    
    def analyze_temporal_capacity_factors(self):
        """分析时序容量因子推算可能性"""
        print("\n3. 时序容量因子分析")
        print("-" * 60)
        
        print("当前数据状况:")
        print("✗ 缺少月度/季度发电量数据")
        print("✗ 缺少流域水文数据")
        print("✗ 缺少历史运行数据")
        
        print("\n时序容量因子推算方法:")
        
        # 方法1: 基于地理位置的气候分区
        print("\n方法1: 基于地理位置的气候分区推算")
        print("原理: 不同气候区域的水文特征具有典型的季节性变化模式")
        
        # 简化的气候分区
        def classify_climate_zone(row):
            lat = row['latitude']
            lon = row['longitude']
            
            if abs(lat) < 23.5:  # 热带
                return 'tropical'
            elif 23.5 <= abs(lat) < 35:  # 亚热带
                return 'subtropical'
            elif 35 <= abs(lat) < 50:  # 温带
                return 'temperate'
            else:  # 寒带
                return 'polar'
        
        self.gdf['climate_zone'] = self.gdf.apply(classify_climate_zone, axis=1)
        climate_stats = self.gdf['climate_zone'].value_counts()
        
        print("气候分区统计:")
        for zone, count in climate_stats.items():
            percentage = (count / len(self.gdf)) * 100
            print(f"  {zone.capitalize()}: {count:,} 个站点 ({percentage:.1f}%)")
        
        # 典型容量因子季节变化模式
        seasonal_patterns = {
            'tropical': {
                'description': '热带地区 - 雨季/旱季明显',
                'monthly_cf': [0.6, 0.7, 0.8, 0.9, 0.8, 0.5, 0.3, 0.2, 0.3, 0.4, 0.5, 0.6],
                'characteristics': '雨季(4-9月)高，旱季(10-3月)低'
            },
            'subtropical': {
                'description': '亚热带地区 - 夏季丰水期',
                'monthly_cf': [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.8, 0.7, 0.5, 0.4, 0.3],
                'characteristics': '夏季(6-8月)最高，冬季(12-2月)最低'
            },
            'temperate': {
                'description': '温带地区 - 春季融雪丰水',
                'monthly_cf': [0.4, 0.5, 0.7, 0.8, 0.9, 0.7, 0.6, 0.5, 0.5, 0.4, 0.4, 0.4],
                'characteristics': '春季(3-5月)融雪期最高'
            },
            'polar': {
                'description': '寒带地区 - 夏季短暂丰水',
                'monthly_cf': [0.2, 0.2, 0.3, 0.5, 0.8, 0.9, 0.8, 0.6, 0.4, 0.3, 0.2, 0.2],
                'characteristics': '夏季(6-8月)融雪期集中'
            }
        }
        
        print("\n典型季节变化模式:")
        for zone, pattern in seasonal_patterns.items():
            print(f"  {pattern['description']}:")
            print(f"    特征: {pattern['characteristics']}")
            print(f"    年平均CF: {np.mean(pattern['monthly_cf']):.2f}")
            print(f"    变异系数: {np.std(pattern['monthly_cf'])/np.mean(pattern['monthly_cf']):.2f}")
        
        # 方法2: 基于站点类型的差异化处理
        print("\n方法2: 基于站点类型的差异化处理")
        
        type_cf_characteristics = {
            'reservoir': {
                'description': '大坝式水电站 - 具有调节能力',
                'base_cf': 0.45,
                'seasonal_variation': 0.15,  # ±15%变化
                'regulation_capability': 'high',
                'monthly_adjustment': 'smooth_seasonal_curve'
            },
            'run_of_river': {
                'description': '引水式水电站 - 跟随径流变化',
                'base_cf': 0.40,
                'seasonal_variation': 0.25,  # ±25%变化
                'regulation_capability': 'low',
                'monthly_adjustment': 'follow_natural_flow'
            }
        }
        
        for station_type, chars in type_cf_characteristics.items():
            type_name = "大坝式" if station_type == 'reservoir' else "引水式"
            print(f"  {type_name}水电站:")
            print(f"    基准容量因子: {chars['base_cf']}")
            print(f"    季节变化幅度: ±{chars['seasonal_variation']*100:.0f}%")
            print(f"    调节能力: {chars['regulation_capability']}")
        
        # 方法3: 基于LCOE的间接推算
        print("\n方法3: 基于LCOE的间接推算")
        print("原理: LCOE较低的站点通常具有更好的水文条件和更高的容量因子")
        
        # LCOE分位数分析
        lcoe_quartiles = self.gdf['LCOE'].quantile([0.25, 0.5, 0.75])
        print("LCOE分位数分析:")
        print(f"  25%分位数: {lcoe_quartiles[0.25]:.4f} USD/kWh")
        print(f"  50%分位数: {lcoe_quartiles[0.5]:.4f} USD/kWh")
        print(f"  75%分位数: {lcoe_quartiles[0.75]:.4f} USD/kWh")
        
        # 基于LCOE的容量因子估算
        def estimate_cf_from_lcoe(lcoe):
            """基于LCOE估算容量因子"""
            if lcoe <= lcoe_quartiles[0.25]:
                return 0.55  # 高容量因子
            elif lcoe <= lcoe_quartiles[0.5]:
                return 0.45  # 中等容量因子
            elif lcoe <= lcoe_quartiles[0.75]:
                return 0.35  # 较低容量因子
            else:
                return 0.25  # 低容量因子
        
        self.gdf['estimated_cf'] = self.gdf['LCOE'].apply(estimate_cf_from_lcoe)
        
        print("\n基于LCOE的容量因子估算结果:")
        cf_stats = self.gdf.groupby('station_type')['estimated_cf'].agg(['mean', 'std', 'min', 'max'])
        for station_type in cf_stats.index:
            type_name = "大坝式" if station_type == 'reservoir' else "引水式"
            stats = cf_stats.loc[station_type]
            print(f"  {type_name}: 平均CF={stats['mean']:.3f}, 标准差={stats['std']:.3f}")
        
        self.analysis_results['temporal_analysis'] = {
            'climate_zones': climate_stats.to_dict(),
            'seasonal_patterns': seasonal_patterns,
            'station_type_characteristics': type_cf_characteristics,
            'lcoe_based_estimation': {
                'quartiles': lcoe_quartiles.to_dict(),
                'average_cf_by_type': cf_stats['mean'].to_dict()
            }
        }
        
        return True
    
    def generate_milp_data_preparation_plan(self):
        """生成MILP模型数据准备方案"""
        print("\n4. MILP模型数据准备方案")
        print("-" * 60)
        
        print("数据转换流程:")
        print("步骤1: 装机容量计算")
        print("  公式: Capacity(MW) = Power(kWh) / (8760 × CF)")
        print("  建议CF: 大坝式0.45, 引水式0.40")
        
        print("\n步骤2: 投资成本转换")
        print("  公式: IC($/MW) = LCOE($/kWh) × 8760 × CF × 1000 / CRF")
        print("  其中CRF为资本回收因子")
        
        # 计算投资成本示例
        crf = 0.08  # 假设8%的资本回收因子
        self.gdf['investment_cost_per_mw'] = (self.gdf['LCOE'] * 8760 * 
                                            self.gdf['estimated_cf'] * 1000 / crf)
        
        print(f"\n投资成本计算结果:")
        print(f"  平均投资成本: {self.gdf['investment_cost_per_mw'].mean():,.0f} $/MW")
        print(f"  成本范围: {self.gdf['investment_cost_per_mw'].min():,.0f} - {self.gdf['investment_cost_per_mw'].max():,.0f} $/MW")
        
        print("\n步骤3: 时序容量因子生成")
        print("  方法A: 基于气候分区的典型模式")
        print("  方法B: 基于站点类型的差异化处理")
        print("  方法C: 基于LCOE的间接推算")
        
        print("\n步骤4: 国家级聚合")
        print("  使用国家边界shapefile进行空间连接")
        print("  按国家汇总装机潜力和成本参数")
        
        # 生成示例月度容量因子
        def generate_monthly_cf(row):
            """为每个站点生成月度容量因子"""
            climate_zone = row['climate_zone']
            station_type = row['station_type']
            base_cf = row['estimated_cf']
            
            # 获取气候区域的季节模式
            if climate_zone in seasonal_patterns:
                pattern = seasonal_patterns[climate_zone]['monthly_cf']
                # 标准化到基准容量因子
                normalized_pattern = np.array(pattern) * (base_cf / np.mean(pattern))
                
                # 根据站点类型调整
                if station_type == 'reservoir':
                    # 大坝式：平滑化处理
                    smoothed = np.convolve(normalized_pattern, [0.3, 0.4, 0.3], mode='same')
                    return smoothed.tolist()
                else:
                    # 引水式：保持原始变化
                    return normalized_pattern.tolist()
            else:
                # 默认均匀分布
                return [base_cf] * 12
        
        # 为前100个站点生成示例
        sample_data = self.gdf.head(100).copy()
        sample_data['monthly_cf'] = sample_data.apply(generate_monthly_cf, axis=1)
        
        print("\n示例月度容量因子生成 (前5个站点):")
        for i in range(5):
            row = sample_data.iloc[i]
            monthly_cf = row['monthly_cf']
            print(f"  站点{i+1} ({row['station_type']}, {row['climate_zone']}):")
            print(f"    年平均CF: {np.mean(monthly_cf):.3f}")
            print(f"    月度CF: {[f'{cf:.2f}' for cf in monthly_cf[:6]}... (仅显示前6个月)")
        
        # 数据质量评估
        print("\n数据质量评估:")
        quality_metrics = {
            'spatial_coverage': '全球覆盖，124,761个站点',
            'data_completeness': '100% (无缺失值)',
            'capacity_calculation': '可基于Power字段计算',
            'cost_data': 'LCOE数据完整',
            'temporal_data': '需要基于方法学推算',
            'validation_needed': '需要与实际数据验证'
        }
        
        for metric, status in quality_metrics.items():
            print(f"  {metric}: {status}")
        
        self.analysis_results['milp_preparation'] = {
            'capacity_calculation_method': 'Power / (8760 × CF)',
            'investment_cost_method': 'LCOE × 8760 × CF × 1000 / CRF',
            'temporal_cf_methods': ['climate_zone', 'station_type', 'lcoe_based'],
            'data_quality_metrics': quality_metrics,
            'sample_monthly_cf': sample_data[['station_type', 'climate_zone', 'estimated_cf', 'monthly_cf']].head().to_dict('records')
        }
        
        return True
    
    def save_results(self):
        """保存分析结果"""
        # 保存完整分析结果
        with open('capacity_analysis_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存处理后的数据样本
        sample_output = self.gdf[['longitude', 'latitude', 'Class', 'station_type', 
                                'LCOE', 'Power', 'capacity_mw', 'climate_zone', 
                                'estimated_cf', 'investment_cost_per_mw']].head(1000)
        sample_output.to_csv('hydropower_sample_with_capacity.csv', index=False)
        
        print("\n5. 分析结果保存")
        print("-" * 60)
        print("✓ 完整分析结果: capacity_analysis_results.json")
        print("✓ 处理后数据样本: hydropower_sample_with_capacity.csv")
        print(f"✓ 总装机潜力: {self.gdf['capacity_mw'].sum():,.0f} MW")
        print(f"✓ 平均投资成本: {self.gdf['investment_cost_per_mw'].mean():,.0f} $/MW")
        
        return True

def main():
    """主函数"""
    shapefile_path = "/Users/<USER>/Downloads/文献附件下载/GlobalHydropower-main/HydroStation-NW/HydroStation/HydroStation.shp"
    
    analyzer = HydropowerCapacityAnalyzer(shapefile_path)
    
    # 执行分析流程
    analyzer.load_and_prepare_data()
    analyzer.analyze_capacity_calculation()
    analyzer.analyze_station_type_differences()
    analyzer.analyze_temporal_capacity_factors()
    analyzer.generate_milp_data_preparation_plan()
    analyzer.save_results()
    
    print("\n" + "="*80)
    print("分析完成！")
    print("="*80)

if __name__ == "__main__":
    main()
